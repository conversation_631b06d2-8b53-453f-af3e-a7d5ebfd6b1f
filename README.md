#### 一、项目简介
+ 一直以来都有一个社交梦，想做一款IM应用，看了很多优秀的开源项目，但是没有合适自己的。于是利用休息时间自己写了这么一套系统。
+ 项目第一个版本历时2个月，前端使用`uniapp`，后端使用`SpringBoot`。
+ 页面设计后期会出`独立UI`。
+ 手机端使用`uniapp`实现，目前仅支持`安卓端`、`iOS端`和`H5端`，后期会继续适配`小程序端`、`桌面端`（windows、mac）和`web端`。
+ 您的支持，就是我们`【生发的动力】`,请手动点个`star`吧。
+ 前端源码地址：[https://gitee.com/lakaola/im-uniapp](https://gitee.com/lakaola/im-uniapp)
+ 后端源码地址：[https://gitee.com/lakaola/im-platform](https://gitee.com/lakaola/im-platform)
+ 下载体验地址：[https://xaldit.com/weiliao/index.html](https://xaldit.com/weiliao/index.html)
+ 加入QQ群：[![加入QQ群](https://img.shields.io/badge/加入QQ群-535099683-blue.svg)](https://jq.qq.com/?_wv=1027&k=PQMnFugm)（535099683）

#### 二、使用须知
+ 仅允许`技术学习`使用
+ 不允许`本产品及其衍生品`进行任何形式`商业使用`
+ 请自觉遵守本开源协议（MPL-2.0），再次开源请注明出处
+ 推荐Watch、Star项目，获取项目第一时间更新，同时也是对项目最好的支持
+ 希望大家多多支持本开源作品

#### 三、技术使用
+ 推送：uniPush + websocket
+ 资源：阿里OSS（图片、声音、视频、文件等）
+ 音视频：TRTC
+ 地图：高德地图
+ 短信：阿里云短信
+ 后端：Hutool、MyBatis-Plus、shiro、sharding-jdbc、接口版本控制等
+ 前端：uniapp(Vue3)

#### 四、演示效果
<img src="https://img.alicdn.com/imgextra/i3/87413133/O1CN01bZSz2q1Z0xco96F1t_!!87413133.jpg" width="200">
<img src="https://img.alicdn.com/imgextra/i3/87413133/O1CN01Pe8G6S1Z0xcmQluDI_!!87413133.jpg" width="200">
<img src="https://img.alicdn.com/imgextra/i1/87413133/O1CN012JP8VW1Z0xccuWKzM_!!87413133.jpg" width="200">
<img src="https://img.alicdn.com/imgextra/i4/87413133/O1CN01fMUNJA1Z0xck1w0kt_!!87413133.jpg" width="200">
<img src="https://img.alicdn.com/imgextra/i3/87413133/O1CN01n8MZhZ1Z0xctYZEbM_!!87413133.jpg" width="200">

#### 五、请作者喝杯茶吧
<img src="https://img.alicdn.com/imgextra/i3/87413133/O1CN01Ilrbqk1Z0xcwW5PsK_!!87413133.jpg" width="600">

#### 六、项目计划
+ 适配android端（已完成）
+ 适配iOS端（已完成）
+ 适配H5端（已完成）
+ 适配PC端[Windows]
+ 适配PC端[Mac]
+ 适配WEB端[后台管理集成通信]
+ 增加后台管理端[管理后台]
+ 更换手机端UI[独立UI]
+ 增加通知栏消息展示[当app后台挂起，通知栏展示消息]
+ 各大应用市场上架
+ 后端优化，支持大并发
+ ...

#### 七、版本迭代    
+ 1.2.0   
    1、修复群名称、昵称显示问题   
    2、修复性别显示问题   
    3、修复部分手机日期显示问题   
    4、修改消息滚动问题   
+ 1.1.0  
    1、适配H5端  
    2、集成WebSocket通讯  
    3、修改注册后不跳转至登录页问题  
    4、修改长按消息弹出方式   
    5、修改群聊个人头像点击跳转无信息问题  
    6、修改附近的人显示问题     
    7、修改摇一摇显示问题     
    8、修改其他问题  
+ 1.0.0   
    1、首版震撼发布  
