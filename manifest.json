{
    "name" : "微聊",
    "appid" : "__UNI__CA19A2D",
    "description" : "考拉Team微聊",
    "versionName" : "1.2.2",
    "versionCode" : 122,
    "developer" : {
        "name" : "<EMAIL>",
        "email" : "<EMAIL>"
    },
    "transformPx" : false,
    "app-plus" : {
        "compatible" : {
            "ignoreVersion" : true //是否忽略版本兼容检查提示
        },
        /* 5+App特有相关 */
        "permissions" : {
            "File" : {
                "description" : "文件系统"
            }
        },
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : false,
            "autoclose" : false,
            "delay" : 0
        },
        "modules" : {
            "Geolocation" : {},
            "Maps" : {},
            "Push" : {},
            "Speech" : {},
            "VideoPlayer" : {},
            "iBeacon" : {},
            "Barcode" : {},
            "Camera" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>"
                ],
                "minSdkVersion" : 21
            },
            "ios" : {
                "dSYMs" : false
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "81cc6c72aeb6a1946510cc1e9f87ee80",
                        "appkey_android" : "81cc6c72aeb6a1946510cc1e9f87ee80"
                    }
                },
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "81cc6c72aeb6a1946510cc1e9f87ee80",
                        "appkey_android" : "81cc6c72aeb6a1946510cc1e9f87ee80"
                    }
                },
                "push" : {
                    "unipush" : {
                        "icons" : {
                            "small" : {
                                "ldpi" : "static/push/18.png",
                                "mdpi" : "static/push/24.png",
                                "hdpi" : "static/push/36.png",
                                "xhdpi" : "static/push/48.png",
                                "xxhdpi" : "static/push/72.png"
                            }
                        }
                    }
                },
                "speech" : {
                    "ifly" : {}
                },
                "ad" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "default",
                "android" : {
                    "hdpi" : "unpackage/res/splash/splash480.9.png",
                    "xhdpi" : "unpackage/res/splash/splash750.9.png",
                    "xxhdpi" : "unpackage/res/splash/splash1080.9.png"
                },
                "useOriginalMsgbox" : true
            }
        },
        "uniStatistics" : {
            "enable" : true
        },
        "nativePlugins" : {
            "TUICallingUniPlugin-TUICallingModule" : {
                "__plugin_info__" : {
                    "name" : "TUICallingUniPlugin-TUICallingModule",
                    "description" : "腾讯云音视频插件",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {}
                }
            }
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "vueVersion" : "3",
    "uniStatistics" : {
        "enable" : true,
        "version" : "1"
    },
    "h5" : {
        "uniStatistics" : {
            "enable" : true
        },
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "3U7BZ-AZZKD-IXC4C-HZA7T-2PGKT-EZFER"
                }
            }
        }
    },
    "mp-alipay" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-baidu" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-kuaishou" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-lark" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-qq" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-toutiao" : {
        "uniStatistics" : {
            "enable" : true
        }
    }
}
