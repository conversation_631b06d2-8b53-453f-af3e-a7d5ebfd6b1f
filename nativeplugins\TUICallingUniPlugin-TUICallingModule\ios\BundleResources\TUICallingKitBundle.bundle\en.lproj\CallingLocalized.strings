/* 
  CallingLocalized.strings
  TRTCCalling

  Created by adams on 2021/5/13.
  
*/

"LoginNetwork.ProfileManager.sendfailed" = "Send failed. Please try again later.";
"V2.Live.LoginMock.sendtheverificatcode" = "Please send the verification code";
"LoginNetwork.ProfileManager.loginfailed" = "Login failed. Please try again later.";
"LoginNetwork.ProfileManager.queryfailed" = "Query failed. Please try again later.";
"LoginNetwork.ProfileManager.registerfailed" = "Registration failed. Please try again later";
"Demo.TRTC.calling.callingrequest" = "You have a call invitation.";
"Demo.TRTC.calling.syserror"= "System error";
"Demo.TRTC.calling.yourphonenumber" = "Your mobile number ";
"Demo.TRTC.calling.searchphonenumber" = "Mobile number";
"Demo.TRTC.calling.searching" = "Search";
"Demo.TRTC.calling.searchandcall" = "Search for a registered user\n to start a call";
"Demo.TRTC.calling.callingbegan" = "Joined call";
"Demo.TRTC.calling.callingcancel" = "Cancelled the call";
"Demo.TRTC.calling.callingtimeout" = "Call timed out";
"Demo.TRTC.calling.callingrefuse" = "declined the call";
"Demo.TRTC.calling.callingleave" = "left the call";
"Demo.TRTC.calling.callingnoresponse" = "didn’t answer";
"Demo.TRTC.calling.callingbusy" = "is busy";
"Demo.TRTC.calling.searchingfailed" = "Search failed";
"Demo.TRTC.calling.cantinviteself" = "You cannot invite yourself.";
"Demo.TRTC.calling.muteon" = "Muted";
"Demo.TRTC.calling.muteoff" = "Unmuted";
"Demo.TRTC.calling.handsfreeon" = "Speaker on";
"Demo.TRTC.calling.handsfreeoff" = "Speaker off";
"LoginNetwork.AppUtils.warmprompt" = "Note";
"LoginNetwork.AppUtils.tomeettheregulatory" = "In accordance with relevant regulations, the app forbids pornographic, abusive, violent, terrorist, and politically sensitive content. All audio/video calls and live streams are recorded and archived.";
"LoginNetwork.AppUtils.determine" = "OK";
"Demo.TRTC.calling.invitetovideocall" = "invites you to a video call";
"Demo.TRTC.calling.invitetoaudiocall" = "invites you to a audio call";
"Demo.TRTC.Streaming.call" = "Call";

"Demo.TRTC.Calling.waitaccept" = "Waiting for the user to accept...";
"Demo.TRTC.Calling.hangup" = "Hang Up";
"Demo.TRTC.Calling.decline" = "Decline";
"Demo.TRTC.Calling.mic" = "Mic";
"Demo.TRTC.Calling.speaker" = "Speaker";
"Demo.TRTC.Calling.camera" = "Camera";
"Demo.TRTC.Calling.switchtoaudio" = "Switch to Audio Call";
"Demo.TRTC.Calling.answer" = "Answer";
"Demo.TRTC.Calling.othernetworkpoor" = "The other party's network connection is poor";
"Demo.TRTC.Calling.yournetworkpoor" = "Your network connection is poor";
"Demo.TRTC.Salon.invitelimited" = "The request is being processed, please try again later";
"Demo.TRTC.Login.countrycode" = "Country Code";

"Demo.TRTC.Calling.failedtogetcamerapermission" = "Failed to get camera permission. Please go to Privacy > Camera to grant the permission";
"Demo.TRTC.Calling.failedtogetmicrophonepermission" = "Failed to get mic permission. Please go to Privacy > Microphone to grant the permission";
"Demo.TRTC.Calling.calleeTip" = "They also";
