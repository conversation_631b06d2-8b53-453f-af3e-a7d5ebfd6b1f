/* 
  Localizable.strings
  English
  
  Created by harvy on 2020/10/9.
  
*/

"TUIKitTipsMostSelectTextFormat" = "Select %ld at most";

"Confirm" = "OK";
"Cancel" = "Cancel";
"Send" = "Send";
"Save" = "Save";
"You" = "You";
"you" = "you";
"Male" = "Male";
"Female" = "Female";
"File" = "Files";
"Download" = "Download";
"Unsetted" = "Not set";

"Monday" = "Mon";
"Tuesday" = "Tue";
"Wednesday" = "Wed";
"Thursday" = "Thur";
"Friday" = "Fri";
"Saturday" = "Sat";
"Sunday" = "Sun";
"Yesterday" = "Yesterday";
"am" = "AM";
"pm" = "PM";
"YesterdayDateFormat" = "HH:mma";       // 英文是 HH:mma 不要动

"Read" = "Read";
"Unread" = "Unread";

"Copy" = "Copy";
"Delete" = "Delete";
"Revoke" = "Recall";
"Retry" = "Retry";
"Re-send" = "Resend";
"Make-a-call" = "Start Call";
"Done" = "OK";
"All" = "All";
"Living" = "In Progress";
"Live-finished" = "Ended";
"Agreed" = "Agreed";
"Disclined" = "Declined";
"Agree" = "Agree";
"Have-been-sent" = "Sent";
"View-the-original" = "View Full Image";

"TUIKitWelcome" = "Welcome to join the Tencent Cloud Communications family!";
"TUIKitMicCamerAuthTips" = "Enable mic and camera permissions";
"TUIKitMicAuth" = "Enable mic permission";
"TUIKitTipsConfirmResendMessage" = "Resend this message? ";
"TUIKitTipsSystemError" = "System error";
"TUIKitTipsEnterRoomErrorFormat" = "Failed to enter the room: %d";
"TUIKitWhosLiveFormat" = "%@'s live stream";
"TUIKitWhoRequestForJoinGroupFormat" = "%@ requests to join group chat.";
"TUIKitAgreedByAdministor" = "Request approved by admin";
"TUIkitDiscliedByAdministor" = "Admin declined the request.";
"TUIKitDownloadProgressFormat" = "Downloading %d%%";
"TUIKitOpenWithOtherApp" = "Open with another app";
"TUIKitTipsContactListNil" = "Contact list is empty. Add friends first.";

"TUIKitInputHoldToTalk" = "Hold to Talk";
"TUIKitInputReleaseToSend" = "Release to End";
"TUIKitInputReleaseToCancel" = "Release to Cancel";
"TUIKitInputNoMicTitle" = "Failed to access mic";
"TUIKitInputNoMicTips"  = "Enable mic permission to send voice messages";
"TUIKitInputNoMicOperateLater" = "Later";
"TUIKitInputNoMicOperateEnable" = "Enable";
"TUIKitInputBlankMessageTitle" = "Unable to send blank message";
"TUIKitInputWillFinishRecordInSeconds" = "Recording will end in %ld seconds.";
"TUIKitInputRecordSlideToCancel" = "Slide up to cancel";
"TUIKitInputRecordReleaseToCancel" = "Release to Cancel";
"TUIKitInputRecordTimeshort" = "Message too short";
"TUIKitInputRecordTimeLong" = "Message too long";

"TUIKitGroupProfileDetails" = "Details";
"TUIKitGroupProfileMember" = "Group Members";
"TUIKitGroupProfileMemberCount" = "%d member(s)";
"TUIKitGroupProfileMemberCountlu" = "%lu member(s)";
"TUIKitGroupProfileType" = "Group Type";
"TUIKitGroupProfileJoinType" = "Group Joining Method";
"TUIKitGroupProfileInviteJoin" = "Invite";
"TUIKitGroupProfileAutoApproval" = "Auto Approval";
"TUIKitGroupProfileAlias" = "My Alias in Group";
"TUIKitGroupProfileMessageDoNotDisturb" = "Message Do Not Disturb";
"TUIKitGroupProfileStickyOnTop" = "Sticky on Top";
"TUIKitGroupProfileDeleteAndExit" = "Delete and Leave";
"TUIKitGroupProfileDissolve" = "Disband Group";
"TUIKitGroupProfileJoinDisable" = "Prohibited from Joining";
"TUIKitGroupProfileAdminApprove" = "Admin Approval";
"TUIKitGroupProfileEditAlias" = "Edit My Alias in Group";
"TUIKitGroupProfileEditGroupName" = "Edit Group Name";
"TUIKitGroupProfileEditAnnouncement" = "Edit Group Notice";
"TUIKitGroupProfileEditAvatar" = "Change Group Profile Photo";
"TUIKitGroupProfileDeleteGroupTips" = "After leaving, you will no longer receive messages from this group chat.";
"TUIKitGroupProfileGroupCountFormat" = "Group members (%ld in total)";
"TUIKitGroupProfileManage" = "Manage";
"TUIKitGroupProfileManageAdd" = "Add Member";
"TUIKitGroupProfileManageDelete" = "Delete Member";
"TUIKitGroupApplicant" = "Request to Join Group";
"TUIKitGroupDismssTipsFormat" = "Group %@ disbanded";
"TUIKitGroupRecycledTipsFormat" = "%@ group has been reclaimed.";
"TUIKitGroupKickOffTipsFormat" = "You were removed from the group %@.";
"TUIKitGroupDropoutTipsFormat" = "You have left the group %@.";

"TUIKitMessageTipsYouRecallMessage" = "You recalled a message.";
"TUIKitMessageTipsReEditMessage" = "re-edit";
"TUIKitMessageTipsRecallMessageFormat" = "\"%@\" recalled a message.";
"TUIkitMessageTipsOthersRecallMessage" = "The other user recalled a message.";
"TUIKitMessageTipsJoinGroupFormat" = "\"%@\" joined the group.";
"TUIKitMessageTipsInviteJoinGroupFormat" = "\"%@\" invited \"%@\" to join the group.";
"TUIKitMessageTipsLeaveGroupFormat" = "\"%@\" left the group chat.";
"TUIKitMessageTipsKickoffGroupFormat" = "\"%@\" removed \"%@\" from the group.";
"TUIKitMessageTipsSettAdminFormat" = "\"%@\" is set as admin.";
"TUIKitMessageTipsCancelAdminFormat" = "The admin status of \"%@\" is terminated.";
"TUIkitMessageTipsEditGroupNameFormat" = "%@ changed the group name to\"%@\",";
"TUIKitMessageTipsEditGroupIntroFormat" = "%@ changed the group description to \"%@\".";
"TUIKitMessageTipsEditGroupAnnounceFormat" = "%@ changed the group notice to \"%@\".";
"TUIKitMessageTipsEditGroupAvatarFormat" = "%@ changed the group profile photo.";
"TUIKitMessageTipsEditGroupOwnerFormat" = "%@ changed the group owner to \"%@\".";
"TUIKitMessageTipsCreateGroupFormat" = "\"%@\" created a group.";
"TUIKitMessageTipsUnsupportCustomMessage" = "[Custom Messages]";
"TUIKitMessageTipsMute" = "is blocked from posting";
"TUIKitMessageTipsUnmute" = "Unblocked";

"TUIKitSignalingFinishGroupChat" = "End Group Chat";
"TUIKitSignalingFinishConversationAndTimeFormat" = "Duration: %.2d:%.2d.";
"TUIKitSignalingNewCall" = "Start Call";
"TUIKitSignalingNewGroupCallFormat" = "\"%@\" initiated a group call.";
"TUIkitSignalingCancelCall" = "Cancel Call";
"TUIkitSignalingCancelGroupCallFormat" = "\"%@\" canceled the group call.";
"TUIkitSignalingHangonCall" = "Answered";
"TUIKitSignalingHangonCallFormat" = "\"%@\" answered.";
"TUIKitSignalingBusyFormat" = "\"%@\" is busy.";
"TUIKitSignalingDeclineFormat" = "\"%@\" declined the call.";
"TUIKitSignalingCallBusy" = "Line busy";
"TUIkitSignalingDecline" = "Decline Call";
"TUIKitSignalingNoResponse" = "No answer";
"TUIkitSignalingUnrecognlize" = "Unrecognized call instruction";

"TUIkitMessageTypeImage" = "[Image]";
"TUIKitMessageTypeVoice" = "[Voice]";
"TUIkitMessageTypeVideo" = "[Video]";
"TUIkitMessageTypeFile" = "[File]";
"TUIKitMessageTypeAnimateEmoji" = "[Animated Sticker]";
"TUIKitMessageTypeDraftFormat" = "[Drafts]";

"TUIKitMoreCamera" = "Take Photo";
"TUIKitMorePhoto" = "Album";
"TUIKitMoreVideo" = "Video";
"TUIKitMoreVideoCaptureDurationTip" = "record time is too short";
"TUIKitMoreFile" = "Files";
"TUIKitMoreVideoCall" = "Video Call";
"TUIKitMoreVoiceCall" = "Voice Call";
"TUIKitMoreGroupLive" = "Group Live";
"TUIKitMoreLink" = "Custom";
"TUIKitMoreLinkDetails" = "View Details>>";

"TUIKitCallInviteYouVideoCall" = "invited you to a video call.";
"TUIKitCallTurningOnMute" = "Mute On";
"TUIKitCallTurningOffMute" = "Mute Off";
"TUIKitCallUsingSpeaker" = "Turn On Speaker";
"TUIKitCallUsingHeadphone" = "Turn Off Speaker";
"TUIKitCallCancelCallingFormat" = "%@ canceled the call.";

"TUIKitAtSelectMemberTitle" = "Select Group Members";

"TUIKitConversationTipsAtMe" = "[You were mentioned]";
"TUIKitConversationTipsAtAll" = "[@All]";
"TUIKitConversationTipsAtMeAndAll" = "[I was mentioned][@All]";

"TUIKitPublicGroup" = "Public Group";
"TUIKitWorkGroup" = "Discussion Group";
"TUIKitChatRoom" = "Chatroom";
"TUIKitCommunity" = "Community";

"TUIKitContactsNewFriends" = "New Contacts";
"TUIKitContactsGroupChats" = "Group Chat";
"TUIKitContactsBlackList" = "Blocked List";
"TUIKitAddFriendSourceFormat" = "Source: %@";
"TUIKitFriendApplicationApproved" = "Friend request accepted";
"TUIKitFirendRequestRejected" = "Friend request declined";

"TUIKitOfflinePushCallTips" = "You have a call request.";

"TUIKitChatPendencyTitle" = "Tap to Process";
"TUIKitChatPendencyRequestToJoinGroupFormat" = "%@ group joining request(s)";

"TUIKitSignalingLiveRequestForMic" = "Apply to Turn on Mic in Live Room";
"TUIKitSignalingLiveRequestForMicRejected" = "Application for turning on mic in live room declined";
"TUIKitSignalingAgreeMicRequest" = "Application for turning on mic in live room approved";
"TUIKitSignalingCloseLinkMicRequest" = "Apply to End Co-anchor in Live Room";
"TUIKitSignalingCloseLinkMic" = "End Co-anchor in Live Room";
"TUIKitSignalingRequestForPK" = "Request to PK in Live Room";
"TUIKitSignalingRequestForPKRejected" = "Request to PK in live room declined";
"TUIKitSignalingRequestForPKAgree" = "Request to PK in live room approved";
"TUIKitSignalingPKExit" = "Exit PK in Live Room";

"TUIKitAllowTypeAcceptOne" = "Allow any user to add you as friend";
"TUIKitAllowTypeNeedConfirm" = "Anyone upon Request";
"TUIKitAllowTypeDeclineAll" = "Decline friend request from any user";

"TUIKitErrorInProcess" = "Executing";
"TUIKitErrorInvalidParameters" = "Invalid parameter";
"TUIKitErrorIOOperateFaild" = "Local IO operation error";
"TUIKitErrorInvalidJson" = "Invalid JSON format";
"TUIKitErrorOutOfMemory" = "Out of storage";
"TUIKitErrorParseResponseFaild" = "PB parsing failed";
"TUIKitErrorSerializeReqFaild" = "PB serialization failed";
"TUIKitErrorSDKNotInit" = "IM SDK is not initialized.";
"TUIKitErrorLoadMsgFailed" = "Failed to load local database";
"TUIKitErrorDatabaseOperateFailed" = "Local database operation failed";
"TUIKitErrorCrossThread" = "Cross-thread error";
"TUIKitErrorTinyIdEmpty" = "User info is empty.";
"TUIKitErrorInvalidIdentifier" = "Invalid identifier";
"TUIKitErrorFileNotFound" = "File not found";
"TUIKitErrorFileTooLarge" = "File size exceeds the limit.";
"TUIKitErrorEmptyFile" = "Empty file";
"TUIKitErrorFileOpenFailed" = "Failed to open file";
"TUIKitErrorNotLogin" = "Not logged in to IM SDK";
"TUIKitErrorNoPreviousLogin" = "Not logged in to the user's account.";
"TUIKitErrorUserSigExpired" = "UserSig expired";
"TUIKitErrorLoginKickedOffByOther" = "Log in to the same account on other devices";
"TUIKitErrorTLSSDKInit" = "TLS SDK initialization failed";
"TUIKitErrorTLSSDKUninit" = "TLS SDK is not initialized.";
"TUIKitErrorTLSSDKTRANSPackageFormat" = "Invalid TLS SDK TRANS packet format";
"TUIKitErrorTLSDecrypt" = "TLS SDK decryption failed";
"TUIKitErrorTLSSDKRequest" = "TLS SDK request failed";
"TUIKitErrorTLSSDKRequestTimeout" = "TLS SDK request timed out";
"TUIKitErrorInvalidConveration" = "Invalid session";
"TUIKitErrorFileTransAuthFailed" = "Authentication failed during file transfer.";
"TUIKitErrorFileTransNoServer" = "Failed to get the server list via FTP.";
"TUIKitErrorFileTransUploadFailed" = "Failed to upload the file via FTP. Check your network connection.";
"TUIKitErrorFileTransUploadFailedNotImage" = "Failed to upload the file via FTP. Check if the uploaded image can be opened normally.";
"TUIKitErrorFileTransDownloadFailed" = "Failed to download the file via FTP. Check whether your network is connected or the file or audio has expired.";
"TUIKitErrorHTTPRequestFailed" = "HTTP request failed";
"TUIKitErrorInvalidMsgElem" = "Invalid IM SDK message elem";
"TUIKitErrorInvalidSDKObject" = "Invalid object";
"TUIKitSDKMsgBodySizeLimit" = "Message length exceeds the limit.";
"TUIKitErrorSDKMsgKeyReqDifferRsp" = "Message key error";
"TUIKitErrorSDKGroupInvalidID" = "Invalid group ID. Custom group ID must be printable ASCII characters (0x20-0x7e), with maximum length of 48 bytes, and cannot be prefixed with @TGS#.";
"TUIKitErrorSDKGroupInvalidName" = "Group name is invalid, which cannot exceed 30 bytes.";
"TUIKitErrorSDKGroupInvalidIntroduction" = "Group description is invalid, which cannot exceed 240 bytes.";
"TUIKitErrorSDKGroupInvalidNotification" = "Group notice is invalid, which cannot exceed 300 bytes.";
"TUIKitErrorSDKGroupInvalidFaceURL" = "Group profile photo URL is invalid, which should not exceed 100 bytes.";
"TUIKitErrorSDKGroupInvalidNameCard" = "Group card is invalid, which cannot exceed 50 bytes.";
"TUIKitErrorSDKGroupMemberCountLimit" = "The maximum number of group members is exceeded.";
"TUIKitErrorSDKGroupJoinPrivateGroupDeny" = "Request to join private groups is not allowed.";
"TUIKitErrorSDKGroupInviteSuperDeny" = "Group owners cannot be invited.";
"TUIKitErrorSDKGroupInviteNoMember" = "The number of members to be invited cannot be 0.";
"TUIKitErrorSDKFriendShipInvalidProfileKey" = "Invalid data field";
"TUIKitErrorSDKFriendshipInvalidAddRemark" = "The remark field exceeds the limit of 96 bytes.";
"TUIKitErrorSDKFriendshipInvalidAddWording" = "The description field in the friend request is invalid, which should not exceed 120 bytes.";
"TUIKitErrorSDKFriendshipInvalidAddSource" = "The source field in the friend request is invalid, which should be prefixed with \"AddSource_Type_\".";
"TUIKitErrorSDKFriendshipFriendGroupEmpty" = "The friend list field is invalid. It is required with each list name of 30 bytes at most.";
"TUIKitErrorSDKNetEncodeFailed" = "Network link encryption failed";
"TUIKitErrorSDKNetDecodeFailed" = "Network link decryption failed";
"TUIKitErrorSDKNetAuthInvalid" = "Network link authentication not completed";
"TUIKitErrorSDKNetCompressFailed" = "Unable to compress data packet";
"TUIKitErrorSDKNetUncompressFaile" = "Packet decompression failed";
"TUIKitErrorSDKNetFreqLimit" = "Call frequency is limited, with up to 5 requests per second.";
"TUIKitErrorSDKnetReqCountLimit" = "Request queue is full. The number of concurrent requests exceeds the limit of 1000.";
"TUIKitErrorSDKNetDisconnect" = "Network disconnected. No connection is established, or no network is detected when a socket connection is established.";
"TUIKitErrorSDKNetAllreadyConn" = "Network connection has been established.";
"TUIKitErrorSDKNetConnTimeout" = "Network connection timed out. Try again once network connection is restored.";
"TUIKitErrorSDKNetConnRefuse" = "Network connection denied. Too many attempts. Service denied by the server.";
"TUIKitErrorSDKNetNetUnreach" = "No available route to the network. Try again once network connection is restored.";
"TUIKitErrorSDKNetSocketNoBuff" = "Call failed to due to insufficient buffer resources in the system. System busy. Internal error.";
"TUIKitERRORSDKNetResetByPeer" = "The peer resets the connection.";
"TUIKitErrorSDKNetSOcketInvalid" = "Invalid socket";
"TUIKitErrorSDKNetHostGetAddressFailed" = "IP address resolution failed";
"TUIKitErrorSDKNetConnectReset" = "Network is connected to an intermediate node or connection to the server is reset.";
"TUIKitErrorSDKNetWaitInQueueTimeout" = "Timed out waiting for request packet to enter the to-be-sent queue.";
"TUIKitErrorSDKNetWaitSendTimeout" = "Request packet has entered the to-be-sent queue. Timed out waiting to enter the network buffer of the system.";
"TUIKitErrorSDKNetWaitAckTimeut" = "Request packet has entered the network buffer of the system. Timed out waiting for response from server.";
"TUIKitErrorSDKSVRSSOConnectLimit" = "The number of Server connections exceeds the limit. Service denied by the server.";
"TUIKitErrorSDKSVRSSOVCode" = "Sending verification code timeout.";
"TUIKitErrorSVRSSOD2Expired" = "Key expired. Key is an internal bill generated according to usersig. The validity period of the key is less than or equal to the validity period of usersig. Please call timmanager again getInstance(). The login interface generates a new key.";
"TUIKitErrorSVRA2UpInvalid" = "Ticket expired. Ticket is an internal bill generated according to usersig. The validity period of ticket is less than or equal to that of usersig. Please call timmanager again getInstance(). The login interface generates a new ticket.";
"TUIKitErrorSVRA2DownInvalid" = "The bill failed verification or was hit by security. Please call timmanager again getInstance(). The login interface generates a new ticket.";
"TUIKitErrorSVRSSOEmpeyKey" = "Empty key is not allowed.";
"TUIKitErrorSVRSSOUinInvalid" = "The account in the key does not match the account in the request header.";
"TUIKitErrorSVRSSOVCodeTimeout" = "Timed out sending verification code.";
"TUIKitErrorSVRSSONoImeiAndA2" = "You need to bring your key and ticket.";
"TUIKitErrorSVRSSOCookieInvalid" = "Cookie check mismatch.";
"TUIKitErrorSVRSSODownTips" = "Send a prompt: Key expired.";
"TUIKitErrorSVRSSODisconnect" = "Link disconnected and screen locked.";
"TUIKitErrorSVRSSOIdentifierInvalid" = "Invalid identity.";
"TUIKitErrorSVRSSOClientClose" = "The device automatically logs out.";
"TUIKitErrorSVRSSOMSFSDKQuit" = "MSFSDK automatically logs out.";
"TUIKitErrorSVRSSOD2KeyWrong" = "the number of decryption failures exceeds the threshold, notify the terminal that it needs to be reset, please call timmanager again getInstance(). The login interface generates a new key.";
"TUIKitErrorSVRSSOUnsupport" = "Aggregation is not supported. A unified error code is returned to devices. The device stops aggregation on the persistent TCP connection.";
"TUIKitErrorSVRSSOPrepaidArrears" = "Prepaid service is in arrears.";
"TUIKitErrorSVRSSOPacketWrong" = "Invalid request packet format.";
"TUIKitErrorSVRSSOAppidBlackList" = "SDKAppID blocked list.";
"TUIKitErrorSVRSSOCmdBlackList" = "SDKAppID sets the service cmd blocked list.";
"TUIKitErrorSVRSSOAppidWithoutUsing" = "SDKAppID is disabled.";
"TUIKitErrorSVRSSOFreqLimit" = "Frequency limit (user), which is to limit the number of requests per second of a protocol.";
"TUIKitErrorSVRSSOOverload" = "Packet loss due to overload (system). Service denied by the connected server that failed to process too many requests.";
"TUIKitErrorSVRResNotFound" = "The resource file to be sent does not exist.";
"TUIKitErrorSVRResAccessDeny" = "Unable to access the resource file to be sent.";
"TUIKitErrorSVRResSizeLimit"= "File size exceeds the limit.";
"TUIKitErrorSVRResSendCancel" = "Sending is canceled by the user due to reasons like logging out when sending a message.";
"TUIKitErrorSVRResReadFailed" = "Failed to access the file content.";
"TUIKitErrorSVRResTransferTimeout" = "Timed out transferring the resource file.";
"TUIKitErrorSVRResInvalidParameters" = "Invalid parameter.";
"TUIKitErrorSVRResInvalidFileMd5" = "File MD5 verification failed.";
"TUIKitErrorSVRResInvalidPartMd5" = "Sharding MD5 verification failed.";
"TUIKitErrorSVRCommonInvalidHttpUrl" = "HTTP parsing error. Check the HTTP request URL format.";
"TUIKitErrorSVRCommomReqJsonParseFailed" = "JSON parsing error in the HTTP request. Check the JSON format.";
"TUIKitErrorSVRCommonInvalidAccount" = "The Identifier or UserSig in the request URI or JSON packet is incorrect.";
"TUIKitErrorSVRCommonInvalidSdkappid" = "Invalid SDKAppID. Check the SDKAppID validity.";
"TUIKitErrorSVRCommonRestFreqLimit" = "The REST API call frequency exceeds the limit. Reduce the request rate.";
"TUIKitErrorSVRCommonRequestTimeout" = "The service request timed out or the HTTP request format is incorrect. Check the error and try again.";
"TUIKitErrorSVRCommonInvalidRes" = "Requested resource error. Check the request URL.";
"TUIKitErrorSVRCommonIDNotAdmin" = "Fill in the Identifier field of the REST API request with the app admin's account.";
"TUIKitErrorSVRCommonSdkappidFreqLimit" = "SDKAppID request rate exceeds the limit. Reduce the request rate.";
"TUIKitErrorSVRCommonSdkappidMiss" = "SDKAppID is required for the REST API. Check the SDKAppID in the request URL.";
"TUIKitErrorSVRCommonRspJsonParseFailed" = "JSON parsing error in the HTTP response packet.";
"TUIKitErrorSVRCommonExchangeAccountTimeout" = "Account switching timed out.";
"TUIKitErrorSVRCommonInvalidIdFormat" = "The Identifier type of the request packet body is incorrect. Confirm that the Identifier is a string.";
"TUIKitErrorSVRCommonSDkappidForbidden" = "SDKAppID is disabled.";
"TUIKitErrorSVRCommonReqForbidden" = "Request is disabled.";
"TUIKitErrorSVRCommonReqFreqLimit" = "Too many requests. Try again later.";
"TUIKitErrorSVRCommonInvalidService" = "Your professional package has expired and been deactivated. Please log in to the im purchase page to re purchase the package. After purchase, it will take effect in 5 minutes.";
"TUIKitErrorSVRCommonSensitiveText" = "Text is filtered due to security reasons, which may contain sensitive words.";
"TUIKitErrorSVRCommonBodySizeLimit" = "The sending message package is too long. Currently, the maximum 8K message package length is supported. Please reduce the package size and try again.";
"TUIKitErrorSVRAccountUserSigExpired" = "UserSig has expired. Generate a new one.";
"TUIKitErrorSVRAccountUserSigEmpty" = "UserSig length is 0.";
"TUIKitErrorSVRAccountUserSigCheckFailed" = "UserSig verification failed.";
"TUIKitErrorSVRAccountUserSigMismatchPublicKey" = "Failed to verify UserSig with public key";
"TUIKitErrorSVRAccountUserSigMismatchId" = "The requested Identifier does not match the Identifier that is used to generate the UserSig.";
"TUIKitErrorSVRAccountUserSigMismatchSdkAppid" = "The requested SDKAppID does not match the SDKAppID of the generated UserSig.";
"TUIKitErrorSVRAccountUserSigPublicKeyNotFound" = "Public key does not exist when verifying UserSig.";
"TUIKitErrorSVRAccountUserSigSdkAppidNotFount" = "SDKAppID not found. Check the app information in the IM console.";
"TUIKitErrorSVRAccountInvalidUserSig" = "UserSig has expired. Generate a new one and try again.";
"TUIKitErrorSVRAccountNotFound" = "Requested user account not found.";
"TUIKitErrorSVRAccountSecRstr" = "Restricted for security reasons.";
"TUIKitErrorSVRAccountInternalTimeout" = "Internal server timeout. Try again.";
"TUIKitErrorSVRAccountInvalidCount" = "Invalid batch quantity in the request.";
"TUIkitErrorSVRAccountINvalidParameters" = "Invalid parameter. Check whether the fields are entered as required in the protocol.";
"TUIKitErrorSVRAccountAdminRequired" = "The request requires app admin permissions.";
"TUIKitErrorSVRAccountFreqLimit" = "Restricted due to too many failures and retries. Check if the UserSig is correct and try again after one minute.";
"TUIKitErrorSVRAccountBlackList" = "The account is added to the blocked list.";
"TUIKitErrorSVRAccountCountLimit" = "The number of accounts created exceeds that allowed in the free trial version. Upgrade to the professional version.";
"TUIKitErrorSVRAccountInternalError" = "Internal server error. Try again.";
"TUIKitErrorSVRProfileInvalidParameters" = "Request parameter error. Check if the request is correct according to the error message.";
"TUIKitErrorSVRProfileAccountMiss" = "Request parameter error. No user account specified to pull data.";
"TUIKitErrorSVRProfileAccountNotFound" = "Requested user account not found.";
"TUIKitErrorSVRProfileAdminRequired" = "The request requires app admin permissions.";
"TUIKitErrorSVRProfileSensitiveText" = "The data field contains sensitive words.";
"TUIKitErrorSVRProfileInternalError" = "Server internal error. Try again later.";
"TUIKitErrorSVRProfileReadWritePermissionRequired" = "You have no permission to read the data field. See the data field for details.";
"TUIKitErrorSVRProfileTagNotFound" = "The tag of the data field does not exist.";
"TUIKitErrorSVRProfileSizeLimit" = "The value of the data field exceeds 500 bytes.";
"TUIKitErrorSVRProfileValueError" = "The value of the standard data field is incorrect. See the standard data field for details.";
"TUIKitErrorSVRProfileInvalidValueFormat" = "The value type of the data field does not match. See the standard data field for details.";
"TUIKitErrorSVRFriendshipInvalidParameters" = "Request parameter error. Check if the request is correct according to the error message.";
"TUIKitErrorSVRFriendshipInvalidSdkAppid" = "SDKAppID does not match.";
"TUIKitErrorSVRFriendshipAccountNotFound" = "Requested user account not found.";
"TUIKitErrorSVRFriendshipAdminRequired" = "The request requires app admin permissions.";
"TUIKitErrorSVRFriendshipSensitiveText" = "The relation chain field contains sensitive words.";
"TUIKitErrorSVRFriendshipNetTimeout" = "Network timed out. Try again later.";
"TUIKitErrorSVRFriendshipWriteConflict" = "A write conflict occurred due to concurrent writes. The batch mode is recommended.";
"TUIKitErrorSVRFriendshipAddFriendDeny" = "The backend blocks the user from initiating friend requests.";
"TUIkitErrorSVRFriendshipCountLimit" = "The number of your friends exceeds the limit.";
"TUIKitErrorSVRFriendshipGroupCountLimit" = "The number of lists exceeds the limit.";
"TUIKitErrorSVRFriendshipPendencyLimit" = "You have reached the limit of pending friend requests.";
"TUIKitErrorSVRFriendshipBlacklistLimit" = "The number of accounts in the blocked list exceeds the limit.";
"TUIKitErrorSVRFriendshipPeerFriendLimit" = "The number of the other user's friends exceeds the limit.";
"TUIKitErrorSVRFriendshipInSelfBlacklist" = "You have blocked the other user. Unable to send friend request.";
"TUIKitErrorSVRFriendshipAllowTypeDenyAny" = "The other user's friend request verification mode is \"Decline friend request from any user\".";
"TUIKitErrorSVRFriendshipInPeerBlackList" = "You are blocked by the other user. Unable to send friend request.";
"TUIKitErrorSVRFriendshipAllowTypeNeedConfirm" = "Request sent. Wait for acceptance.";
"TUIKitErrorSVRFriendshipAddFriendSecRstr" = "The friend request was filtered by the security policy. Do not initiate friend requests too frequently.";
"TUIKitErrorSVRFriendshipPendencyNotFound" = "The pending friend request does not exist.";
"TUIKitErrorSVRFriendshipDelFriendSecRstr" = "The friend deletion request was filtered by the security policy. Do not initiate friend deletion requests too frequently.";
"TUIKirErrorSVRFriendAccountNotFoundEx" = "Requested user account not found.";
"TUIKitErrorSVRMsgPkgParseFailed" = "Failed to parse the request packet.";
"TUIKitErrorSVRMsgInternalAuthFailed" = "Internal authentication failed.";
"TUIKitErrorSVRMsgInvalidId" = "Invalid identifier";
"TUIKitErrorSVRMsgNetError" = "Network error. Try again.";
"TUIKitErrorSVRMsgPushDeny" = "A callback is triggered before sending a message in the private chat, and the App backend returns \"The message is prohibited from sending\".";
"TUIKitErrorSVRMsgInPeerBlackList" = "Unable to send messages in the private chat as you are blocked by the other user.";
"TUIKitErrorSVRMsgBothNotFriend" = "You are not a friend of this user. Unable to send messages.";
"TUIKitErrorSVRMsgNotPeerFriend" = "Unable to send messages in the private chat as you are not the other user's friend (one-way friend).";
"TUIkitErrorSVRMsgNotSelfFriend" = "Unable to send messages in the private chat as the other user is not your friend (one-way friend).";
"TUIKitErrorSVRMsgShutupDeny" = "Blocked from posting. Unable to send messages.";
"TUIKitErrorSVRMsgRevokeTimeLimit" = "Timed out recalling the message (default is 2 min).";
"TUIKitErrorSVRMsgDelRambleInternalError" = "An internal error occurred while deleting roaming messages.";
"TUIKitErrorSVRMsgJsonParseFailed" = "Failed to parse the JSON packet. Check whether the request packet meets the JSON specifications.";
"TUIKitErrorSVRMsgInvalidJsonBodyFormat" = "MsgBody in the JSON request packet does not conform to the message format description.";
"TUIKitErrorSVRMsgInvalidToAccount" = "The To_Account field is missing from the JSON request packet body or the type of the To_Account field is not Integer.";
"TUIKitErrorSVRMsgInvalidRand" = "The MsgRandom field is missing from the JSON request packet body or the type of the MsgRandom field is not Integer.";
"TUIKitErrorSVRMsgInvalidTimestamp" = "The MsgTimeStamp field is missing from the JSON request packet body or the type of the MsgTimeStamp field is not Integer.";
"TUIKitErrorSVRMsgBodyNotArray" = "The MsgBody type in the JSON request packet body is not Array.";
"TUIKitErrorSVRMsgInvalidJsonFormat" = "The JSON request packet does not conform to the message format description.";
"TUIKitErrorSVRMsgToAccountCountLimit" = "The number of accounts to which messages are sent in batch exceeds 500.";
"TUIKitErrorSVRMsgToAccountNotFound" = "To_Account is not registered or does not exist.";
"TUIKitErrorSVRMsgTimeLimit" = "Invalid offline message storage time (up to 7 days).";
"TUIKitErrorSVRMsgInvalidSyncOtherMachine" = "The type of the SyncOtherMachine field in the JSON request packet body is not Integer.";
"TUIkitErrorSVRMsgInvalidMsgLifeTime" = "The type of the MsgLifeTime field in the JSON request packet body is not Integer.";
"TUIKitErrorSVRMsgBodySizeLimit" = "The length of JSON data packet exceeds the limit. The message packet body shall not exceed 8 KB.";
"TUIKitErrorSVRmsgLongPollingCountLimit" = "Forced logout on the Web page during long polling (the number of online instances on the Web page exceeds the limit).";
"TUIKitErrorSVRGroupApiNameError" = "The API name in the request is incorrect.";
"TUIKitErrorSVRGroupAccountCountLimit" = "The number of accounts in the request packet body exceeds the limit.";
"TUIkitErrorSVRGroupFreqLimit" = "Call frequency is limited. Reduce the call frequency.";
"TUIKitErrorSVRGroupPermissionDeny" = "Insufficient operation permissions";
"TUIKitErrorSVRGroupInvalidReq" = "Invalid request";
"TUIKitErrorSVRGroupSuperNotAllowQuit" = "Group owner is not allowed to leave this group.";
"TUIKitErrorSVRGroupNotFound" = "The group does not exist.";
"TUIKitErrorSVRGroupJsonParseFailed" = "Failed to parse the JSON packet. Check whether the packet body conforms to the JSON format.";
"TUIKitErrorSVRGroupInvalidId" = "The identifier that is used to initiated the operation is invalid. Check whether the identifier of the user who initiated the operation is correct.";
"TUIKitErrorSVRGroupAllreadyMember" = "The invited user is a group member.";
"TUIKitErrorSVRGroupFullMemberCount" = "The number of group members has reached the limit. Unable to add the user in the request to the group.";
"TUIKitErrorSVRGroupInvalidGroupId" = "Invalid group ID. Check whether the group ID is correct.";
"TUIKitErrorSVRGroupRejectFromThirdParty" = "The app backend rejected this operation via a third-party callback.";
"TUIKitErrorSVRGroupShutDeny" = "This user is blocked from posting and thus cannot send messages. Check whether the sender is blocked from posting.";
"TUIKitErrorSVRGroupRspSizeLimit" = "The response packet length exceeds the limit.";
"TUIKitErrorSVRGroupAccountNotFound" = "Requested user account not found.";
"TUIKitErrorSVRGroupGroupIdInUse" = "The group ID has been used. Select another one.";
"TUIKitErrorSVRGroupSendMsgFreqLimit" = "The frequency of sending messages exceeds the limit. Extend the interval between sending messages.";
"TUIKitErrorSVRGroupReqAllreadyBeenProcessed" = "This invitation or request has been processed.";
"TUIKitErrorSVRGroupGroupIdUserdForSuper" = "The group ID has been used by the group owner. It can be used directly.";
"TUIKitErrorSVRGroupSDkAppidDeny" = "The command word used in the SDKAppID request has been disabled.";
"TUIKitErrorSVRGroupRevokeMsgNotFound" = "This message does not exist.";
"TUIKitErrorSVRGroupRevokeMsgTimeLimit" = "Timed out recalling the message (default is 2 min).";
"TUIKitErrorSVRGroupRevokeMsgDeny" = "Unable to recall this message.";
"TUIKitErrorSVRGroupNotAllowRevokeMsg" = "Unable to recall messages in groups of this type.";
"TUIKitErrorSVRGroupRemoveMsgDeny" = "Unable to delete messages of this type.";
"TUIKitErrorSVRGroupNotAllowRemoveMsg" = "Unable to delete messages in the voice/video chat room and the broadcast group of online members.";
"TUIKitErrorSVRGroupAvchatRoomCountLimit" = "The number of created voice/video chat rooms exceeds the limit.";
"TUIKitErrorSVRGroupCountLimit" = "The number of groups that a single user can create and join exceeds the limit.";
"TUIKitErrorSVRGroupMemberCountLimit" = "The number of group members exceeds the limit.";
"TUIKitErrorSVRNoSuccessResult" = "No success result returned for the batch operation.";
"TUIKitErrorSVRToUserInvalid" = "IM: Invalid recipient";
"TUIKitErrorSVRRequestTimeout" = "Request timeout";
"TUIKitErrorSVRInitCoreFail" = "INIT CORE module failed";
"TUIKitErrorExpiredSessionNode" = "SessionNode is null.";
"TUIKitErrorLoggedOutBeforeLoginFinished" = "Logged out before login (returned at login time)";
"TUIKitErrorTLSSDKNotInitialized" = "tlssdk is not initialized.";
"TUIKitErrorTLSSDKUserNotFound" = "The user information for TLSSDK was not found.";
"TUIKitErrorBindFaildRegTimeout" = "Registration timed out";
"TUIKitErrorBindFaildIsBinding" = "The bind operation in progress.";
"TUIKitErrorPacketFailUnknown" = "Unknown error occurred while sending packet";
"TUIKitErrorPacketFailReqNoNet" = "No network connection when sending request packet, which is converted to case ERR_REQ_NO_NET_ON_REQ:";
"TUIKitErrorPacketFailRespNoNet" = "No network connection when sending response packet, which is converted to case ERR_REQ_NO_NET_ON_RSP:";
"TUIKitErrorPacketFailReqNoAuth" = "No permission when sending request packet";
"TUIKitErrorPacketFailSSOErr" = "SSO error";
"TUIKitErrorPacketFailRespTimeout" = "Response timed out";
"TUIKitErrorFriendshipProxySyncing" = "proxy_manager failed to sync SVR data";
"TUIKitErrorFriendshipProxySyncedFail" = "proxy_manager sync failed";
"TUIKitErrorFriendshipProxyLocalCheckErr" = "proxy_manager request parameter is invalid in local check.";
"TUIKitErrorGroupInvalidField" = "group assistant request field contains non-preset fields.";
"TUIKitErrorGroupStoreageDisabled" = "Local storage of group assistant group data is disabled.";
"TUIKitErrorLoadGrpInfoFailed" = "Failed to load groupinfo from storage";
"TUIKitErrorReqNoNetOnReq" = "No network connection when sending request";
"TUIKitErrorReqNoNetOnResp" = "No network connection when sending response";
"TUIKitErrorServiceNotReady" = "QALSDK service is not ready.";
"TUIKitErrorLoginAuthFailed" = "Account verification failed (user info get failed)";
"TUIKitErrorNeverConnectAfterLaunch" = "Failed to connect network when the app is lunched.";
"TUIKitErrorReqFailed" = "QAL execution failed";
"TUIKitErrorReqInvaidReq" = "Invalid request. Invalid toMsgService.";
"TUIKitErrorReqOnverLoaded" = "Request queue is full.";
"TUIKitErrorReqKickOff" = "Forced logout on another device";
"TUIKitErrorReqServiceSuspend" = "Service suspended";
"TUIKitErrorReqInvalidSign" = "SSO signature error";
"TUIKitErrorReqInvalidCookie" = "Invalid SSO cookie";
"TUIKitErrorLoginTlsRspParseFailed" = "TSL response packet is verified at login time. Packet length error.";
"TUIKitErrorLoginOpenMsgTimeout" = "Timeout occurred when OPENSTATSVC attempted to report status to OPENMSG during login.";
"TUIKitErrorLoginOpenMsgRspParseFailed" = "Response parsing failed when OPENSTATSVC reports status to OPENMSG during login.";
"TUIKitErrorLoginTslDecryptFailed" = "TLS decryption failed at login time.";
"TUIKitErrorWifiNeedAuth" = "Verification is required for Wi-Fi connection.";
"TUIKitErrorUserCanceled" = "Canceled by user";
"TUIkitErrorRevokeTimeLimitExceed" = "Timed out recalling the message (default is 2 min).";
"TUIKitErrorLackUGExt" = "Missing UGC extension pack";
"TUIKitErrorAutoLoginNeedUserSig" = "Local ticket for auto login expired. userSig is required for manual login.";
"TUIKitErrorQALNoShortConneAvailable" = "No available SSO for short connections.";
"TUIKitErrorReqContentAttach" = "Message content is filtered due to security reasons.";
"TUIKitErrorLoginSigExpire" = "Returned at login time. Ticket expired.";
"TUIKitErrorSDKHadInit" = "SDK has been initialized. Do not initialize again.";
"TUIKitErrorOpenBDHBase" = "Openbdh error";
"TUIKitErrorRequestNoNetOnReq" = "No network connection when sending request. Try again once network connection is restored.";
"TUIKitErrorRequestNoNetOnRsp" = "No network connection when sending response. Try again once network connection is restored.";
"TUIKitErrorRequestOnverLoaded" = "Request queue is full.";

/***************************** 消息转发 & 消息搜索 *************************/
// 补充
"Multiple" = "Multiple";
"Forward" = "Forward";
"Delete" = "Delete";
"Copy" = "Copy";
"Revoke" = "Revoke";
"Resend" = "Resend";
"Search" = "Search";
"TUIKitCallMicCamAuthTips" = "Please granted the prillage to mic and camera";
"TUIKitCallMicAuthTips" = "Please granted the prillage to mic";
"TUIKitMessageTipsSureToResend" = "Sure to re-send message?";

// 消息转发
"TUIKitRelayNoMessageTips" = "Please select one message at least";
"TUIKitRelayRecentMessages" = "Recent messages";
"TUIKitRelayChatHistory" = "History";
"TUIKitRelaySepcialForbid" = "Audio messages and other special messages cannot be forwarded.";
"TUIKitRelayConfirmForward" = "Forward to?";
"TUIKitRelayOneByOneForward" = "One-by-one forward";
"TUIKitRelayCombineForwad" = "Combine and forward";
"TUIKitRelayGroupChatHistory" = "Chat history for group";
"TUIKitRelayChatHistoryForSomebodyFormat" = "Chat history for %@ and %@";
"TUIKitRelayErrorForwardFormat" = "@\"Forward error, code:%d, desc:%@\"";
"TUIKitRelayTargetCreateNewChat" = "Create new chat";
"TUIKitRelayTargetSelectFromContacts" = "Select from Contacts";
"TUIKitRelayTargetCrateGroupError" = "Create group error";
"TUIKitRelayTargetNoneTips" = "Contact or conversation cannot null";
"TUIKitRelayLayerLimitTips" = "Sorry，the message has over-limit layers";
"TUIKitRelayCompatibleText" = "Unsupported merged message, please upgrade to the latest version.";
"TUIKitRelayUnsupportForward" = "Messages that failed to be sent do not support forwarding";
"TUIKitRelayOneByOnyOverLimit" = "Too many forwarded messages, one by one forwarding is not currently supported";

// 消息搜索
"TUIKitSearchItemHeaderTitleContact" = "Contact";
"TUIKitSearchItemFooterTitleContact" = "More contacts";
"TUIKitSearchItemHeaderTitleGroup" = "Group";
"TUIKitSearchItemFooterTitleGroup" = "More Groups";
"TUIkitSearchItemHeaderTitleChatHistory" = "Chat history";
"TUIKitSearchItemFooterTitleChatHistory" = "More chat history";
"TUIKitSearchResultMatchFormat" = "Included:%@";
"TUIKitSearchResultMatchGroupIDFormat" = "Inclued group ID:%@";
"TUIKitSearchResultMatchGroupMember" = "Inclued group member:";
"TUIKitSearchResultDisplayChatHistoryCountFormat" = "%zd of chat history";
/***************************** 消息转发 & 消息搜索 *************************/


/***************************** 消息回复 & 换肤 *************************/
"Reply" = "Reply";
"TUIKitReplyMessageNotFoundOriginMessage" = "Unable to locate the original message";
"TUIKitClearAllChatHistory" = "Clear chat history";
"TUIKitClearAllChatHistoryTips" = "Sure to clear all chat history?";
"Discline" = "Discline";
"Copied" = "Copied";
"ConfirmDeleteMessage" = "Sure to delete selected messages";
"TUIKitAddFriend" = "Add friend";
"TUIKitAddGroup" = "Add Group";
"TUIKitSearchUserID" = "Search user ID";
"TUIKitSearchGroupID" = "Search group ID";
"TUIKitNoSelfSignature" = "What's Up not set";
"TUIKitSelfSignatureFormat" = "Signature:%@";
/***************************** 消息回复 & 换肤 *************************/

/***************************** 视频、图片加载 & 保存 *************************/
"TUIKitVideoTranscoding" = "video transcoding...";
"TUIKitVideoDownloading" = "video downloading...";
"TUIKitVideoSavedSuccess" = "video saved successfully";
"TUIKitVideoSavedFailed" = "video save failed";
"TUIKitPictureSavedSuccess" = "picture saved successfully";
"TUIKitPictureSavedFailed" = "picture saved failed";
/***************************** 视频、图片加载 & 保存 *************************/
