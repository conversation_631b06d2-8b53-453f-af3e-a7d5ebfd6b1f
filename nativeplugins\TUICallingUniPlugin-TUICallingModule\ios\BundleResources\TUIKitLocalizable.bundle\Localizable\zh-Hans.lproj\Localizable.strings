/* 
  Localizable.strings
  Chinese Simplized

  Created by harvy on 2020/10/9.
  
*/

"TUIKitTipsMostSelectTextFormat" = "最多选择%ld个";

"Confirm" = "确定";
"Cancel" = "取消";
"Send" = "发送";
"Save" = "保存";
"You" = "您";
"you" = "您";
"Male" = "男";
"Female" = "女";
"File" = "文件";
"Download" = "下载";
"Unsetted" = "未设置";

"Monday" = "星期一";
"Tuesday" = "星期二";
"Wednesday" = "星期三";
"Thursday" = "星期四";
"Friday" = "星期五";
"Saturday" = "星期六";
"Sunday" = "星期日";
"Yesterday" = "昨天";
"am" = "上午";
"pm" = "下午";
"YesterdayDateFormat" = "aHH:mm";

"Read" = "已读";
"Unread" = "未读";

"Copy" = "复制";
"Delete" = "删除";
"Revoke" = "撤回";
"Retry" = "重试";
"Re-send" = "重发";
"Make-a-call" = "发起呼叫"; 
"Done" = "完成";
"All" = "所有人";
"Living" = "正在直播";
"Live-finished" = "直播结束";
"Agreed" = "已同意";
"Disclined" = "已拒绝";
"Agree" = "同意";
"Have-been-sent" = "已发送";
"View-the-original" = "查看原图";

"TUIKitWelcome" = "欢迎加入腾讯·云通信大家庭！";
"TUIKitMicCamerAuthTips" = "请开启麦克风和摄像头权限";
"TUIKitMicAuth" = "请开启麦克风权限";
"TUIKitTipsConfirmResendMessage" = "确定重发此消息吗? ";
"TUIKitTipsSystemError" = "系统错误";
"TUIKitTipsEnterRoomErrorFormat" = "进房失败: %d";
"TUIKitWhosLiveFormat" = "%@的直播";
"TUIKitWhoRequestForJoinGroupFormat" = "%@申请加入群聊";
"TUIKitAgreedByAdministor" = "管理员同意申请";
"TUIkitDiscliedByAdministor" = "管理员拒绝申请";
"TUIKitDownloadProgressFormat" = "正在下载%d%%";
"TUIKitOpenWithOtherApp" = "用其他应用程序打开";
"TUIKitTipsContactListNil" = "联系人列表为空，请先添加好友";

"TUIKitInputHoldToTalk" = "按住 说话";
"TUIKitInputReleaseToSend" = "松开 结束";
"TUIKitInputReleaseToCancel" = "松开 取消";
"TUIKitInputNoMicTitle" = "无法访问麦克风";
"TUIKitInputNoMicTips"  = "开启麦克风权限才能发送语音消息";
"TUIKitInputNoMicOperateLater" = "以后再说";
"TUIKitInputNoMicOperateEnable" = "去开启";
"TUIKitInputBlankMessageTitle" = "不能发送空白消息";
"TUIKitInputWillFinishRecordInSeconds" = "将在 %ld 秒后结束录制";
"TUIKitInputRecordSlideToCancel" = "手指上滑，取消发送";
"TUIKitInputRecordReleaseToCancel" = "松开手指，取消发送";
"TUIKitInputRecordTimeshort" = "说话时间太短";
"TUIKitInputRecordTimeLong" = "说话时间太长";

"TUIKitGroupProfileDetails" = "详细资料";
"TUIKitGroupProfileMember" = "群成员";
"TUIKitGroupProfileMemberCount" = "%d 人";
"TUIKitGroupProfileMemberCountlu" = "%lu人";
"TUIKitGroupProfileType" = "群类型";
"TUIKitGroupProfileJoinType" = "加群方式";
"TUIKitGroupProfileInviteJoin" = "邀请加入";
"TUIKitGroupProfileAutoApproval" = "自动审批";
"TUIKitGroupProfileAlias" = "我的群昵称";
"TUIKitGroupProfileMessageDoNotDisturb" = "消息免打扰";
"TUIKitGroupProfileStickyOnTop" = "置顶聊天";
"TUIKitGroupProfileDeleteAndExit" = "删除并退出";
"TUIKitGroupProfileDissolve" = "解散该群";
"TUIKitGroupProfileJoinDisable" = "禁止加群";
"TUIKitGroupProfileAdminApprove" = "管理员审批";
"TUIKitGroupProfileEditAlias" = "修改我的群昵称";
"TUIKitGroupProfileEditGroupName" = "修改群名称";
"TUIKitGroupProfileEditAnnouncement" = "修改群公告";
"TUIKitGroupProfileEditAvatar" = "修改群头像";
"TUIKitGroupProfileDeleteGroupTips" = "退出后不会再接收到此群聊消息";
"TUIKitGroupProfileGroupCountFormat" = "群成员(%ld人)";
"TUIKitGroupProfileManage" = "管理";
"TUIKitGroupProfileManageAdd" = "添加成员";
"TUIKitGroupProfileManageDelete" = "删除成员";
"TUIKitGroupApplicant" = "群申请";
"TUIKitGroupDismssTipsFormat" = "%@ 群已解散";
"TUIKitGroupRecycledTipsFormat" = "%@ 群已回收";
"TUIKitGroupKickOffTipsFormat" = "您已被踢出 %@ 群";
"TUIKitGroupDropoutTipsFormat" = "您已退出 %@ 群";

"TUIKitMessageTipsYouRecallMessage" = "您撤回了一条消息";
"TUIKitMessageTipsReEditMessage" = "重新编辑";
"TUIKitMessageTipsRecallMessageFormat" = "\"%@\"撤回了一条消息";
"TUIkitMessageTipsOthersRecallMessage" = "对方撤回了一条消息";
"TUIKitMessageTipsJoinGroupFormat" = "\"%@\"加入群组";
"TUIKitMessageTipsInviteJoinGroupFormat" = "\"%@\"邀请\"%@\"加入群组";
"TUIKitMessageTipsLeaveGroupFormat" = "\"%@\"退出了群聊";
"TUIKitMessageTipsKickoffGroupFormat" = "\"%@\"将\"%@\"踢出群组";
"TUIKitMessageTipsSettAdminFormat" = "\"%@\"被设置管理员";
"TUIKitMessageTipsCancelAdminFormat" = "\"%@\"被取消管理员";
"TUIkitMessageTipsEditGroupNameFormat" = "%@修改群名为\"%@\"、";
"TUIKitMessageTipsEditGroupIntroFormat" = "%@修改群简介为\"%@\"、";
"TUIKitMessageTipsEditGroupAnnounceFormat" = "%@修改群公告为\"%@\"、";
"TUIKitMessageTipsEditGroupAvatarFormat" = "%@修改群头像、";
"TUIKitMessageTipsEditGroupOwnerFormat" = "%@修改群主为\"%@\"、";
"TUIKitMessageTipsCreateGroupFormat" = "\"%@\"创建群组";
"TUIKitMessageTipsUnsupportCustomMessage" = "[自定义消息]";
"TUIKitMessageTipsMute" = "被禁言";
"TUIKitMessageTipsUnmute" = "被解除禁言";

"TUIKitSignalingFinishGroupChat" = "结束群聊";
"TUIKitSignalingFinishConversationAndTimeFormat" = "通话时长：%.2d:%.2d";
"TUIKitSignalingNewCall" = "发起通话";
"TUIKitSignalingNewGroupCallFormat" = "\"%@\" 发起群通话";
"TUIkitSignalingCancelCall" = "取消通话";
"TUIkitSignalingCancelGroupCallFormat" = "\"%@\" 取消群通话";
"TUIkitSignalingHangonCall" = "已接听";
"TUIKitSignalingHangonCallFormat" = "\"%@\" 已接听";
"TUIKitSignalingBusyFormat" = "\"%@\" 忙线";
"TUIKitSignalingDeclineFormat" = "\"%@\" 拒绝通话";
"TUIKitSignalingCallBusy" = "对方忙线";
"TUIkitSignalingDecline" = "拒绝通话";
"TUIKitSignalingNoResponse" = "无应答";
"TUIkitSignalingUnrecognlize" = "不能识别的通话指令";

"TUIkitMessageTypeImage" = "[图片]";
"TUIKitMessageTypeVoice" = "[语音]";
"TUIkitMessageTypeVideo" = "[视频]";
"TUIkitMessageTypeFile" = "[文件]";
"TUIKitMessageTypeAnimateEmoji" = "[动画表情]";
"TUIKitMessageTypeDraftFormat" = "[草稿]";

"TUIKitMoreCamera" = "拍照";
"TUIKitMorePhoto" = "图片";
"TUIKitMoreVideo" = "录像";
"TUIKitMoreVideoCaptureDurationTip" = "录制时间过短";
"TUIKitMoreFile" = "文件";
"TUIKitMoreVideoCall" = "视频通话";
"TUIKitMoreVoiceCall" = "语音通话";
"TUIKitMoreGroupLive" = "群直播";
"TUIKitMoreLink" = "自定义";
"TUIKitMoreLinkDetails" = "查看详情>>";

"TUIKitCallInviteYouVideoCall" = "邀请你视频通话";
"TUIKitCallTurningOnMute" = "开启静音";
"TUIKitCallTurningOffMute" = "关闭静音";
"TUIKitCallUsingSpeaker" = "使用扬声器";
"TUIKitCallUsingHeadphone" = "使用听筒";
"TUIKitCallCancelCallingFormat" = "%@ 取消了通话";

"TUIKitAtSelectMemberTitle" = "选择群成员";

"TUIKitConversationTipsAtMe" = "[有人@我]";
"TUIKitConversationTipsAtAll" = "[@所有人]";
"TUIKitConversationTipsAtMeAndAll" = "[有人@我][@所有人]";

"TUIKitPublicGroup" = "公开群";
"TUIKitWorkGroup" = "讨论组";
"TUIKitChatRoom" = "聊天室";
"TUIKitCommunity" = "社群";

"TUIKitContactsNewFriends" = "新的联系人";
"TUIKitContactsGroupChats" = "群聊";
"TUIKitContactsBlackList" = "黑名单";
"TUIKitAddFriendSourceFormat" = "来源: %@";
"TUIKitFriendApplicationApproved" = "已同意好友申请";
"TUIKitFirendRequestRejected" = "已拒绝好友申请";

"TUIKitOfflinePushCallTips" = "您有一个通话请求";

"TUIKitChatPendencyTitle" = "点击处理";
"TUIKitChatPendencyRequestToJoinGroupFormat" = "%@条入群请求";

"TUIKitSignalingLiveRequestForMic" = "直播间申请上麦";
"TUIKitSignalingLiveRequestForMicRejected" = "直播间申请上麦被拒绝";
"TUIKitSignalingAgreeMicRequest" = "直播间同意上麦";
"TUIKitSignalingCloseLinkMicRequest" = "直播间申请关闭连麦";
"TUIKitSignalingCloseLinkMic" = "直播间关闭连麦";
"TUIKitSignalingRequestForPK" = "直播间申请PK";
"TUIKitSignalingRequestForPKRejected" = "直播间申请PK被拒绝";
"TUIKitSignalingRequestForPKAgree" = "直播间同意PK";
"TUIKitSignalingPKExit" = "直播间退出PK";

"TUIKitAllowTypeAcceptOne" = "同意任何用户加好友";
"TUIKitAllowTypeNeedConfirm" = "需要验证";
"TUIKitAllowTypeDeclineAll" = "拒绝任何人加好友";

"TUIKitErrorInProcess" = "执行中";
"TUIKitErrorInvalidParameters" = "参数无效";
"TUIKitErrorIOOperateFaild" = "操作本地 IO 错误";
"TUIKitErrorInvalidJson" = "错误的 JSON 格式";
"TUIKitErrorOutOfMemory" = "内存不足";
"TUIKitErrorParseResponseFaild" = "PB 解析失败";
"TUIKitErrorSerializeReqFaild" = "PB 序列化失败";
"TUIKitErrorSDKNotInit" = "IM SDK 未初始化";
"TUIKitErrorLoadMsgFailed" = "加载本地数据库操作失败";
"TUIKitErrorDatabaseOperateFailed" = "本地数据库操作失败";
"TUIKitErrorCrossThread" = "跨线程错误";
"TUIKitErrorTinyIdEmpty" = "用户信息为空";
"TUIKitErrorInvalidIdentifier" = "Identifier 非法";
"TUIKitErrorFileNotFound" = "文件不存在";
"TUIKitErrorFileTooLarge" = "文件大小超出了限制";
"TUIKitErrorEmptyFile" = "空文件";
"TUIKitErrorFileOpenFailed" = "文件打开失败";
"TUIKitErrorNotLogin" = "IM SDK 未登陆";
"TUIKitErrorNoPreviousLogin" = "并没有登录过该用户";
"TUIKitErrorUserSigExpired" = "UserSig 过期";
"TUIKitErrorLoginKickedOffByOther" = "其他终端登录同一账号";
"TUIKitErrorTLSSDKInit" = "TLS SDK 初始化失败";
"TUIKitErrorTLSSDKUninit" = "TLS SDK 未初始化";
"TUIKitErrorTLSSDKTRANSPackageFormat" = "TLS SDK TRANS 包格式错误";
"TUIKitErrorTLSDecrypt" = "TLS SDK 解密失败";
"TUIKitErrorTLSSDKRequest" = "TLS SDK 请求失败";
"TUIKitErrorTLSSDKRequestTimeout" = "TLS SDK 请求超时";
"TUIKitErrorInvalidConveration" = "会话无效";
"TUIKitErrorFileTransAuthFailed" = "文件传输鉴权失败";
"TUIKitErrorFileTransNoServer" = "文件传输获取 Server 列表失败";
"TUIKitErrorFileTransUploadFailed" = "文件传输上传失败，请检查网络是否连接";
"TUIKitErrorFileTransUploadFailedNotImage" = "文件传输上传失败，请检查上传的图片是否能够正常打开";
"TUIKitErrorFileTransDownloadFailed" = "文件传输下载失败，请检查网络，或者文件、语音是否已经过期";
"TUIKitErrorHTTPRequestFailed" = "HTTP 请求失败";
"TUIKitErrorInvalidMsgElem" = "IM SDK 无效消息 elem";
"TUIKitErrorInvalidSDKObject" = "无效的对象";
"TUIKitSDKMsgBodySizeLimit" = "消息长度超出限制";
"TUIKitErrorSDKMsgKeyReqDifferRsp" = "消息 KEY 错误";
"TUIKitErrorSDKGroupInvalidID" = "群组 ID 非法，自定义群组 ID 必须为可打印 ASCII 字符（0x20-0x7e），最长48个字节，且前缀不能为 @TGS#";
"TUIKitErrorSDKGroupInvalidName" = "群名称非法，群名称最长30字节";
"TUIKitErrorSDKGroupInvalidIntroduction" = "群简介非法，群简介最长240字节";
"TUIKitErrorSDKGroupInvalidNotification" = "群公告非法，群公告最长300字节";
"TUIKitErrorSDKGroupInvalidFaceURL" = "群头像 URL 非法，群头像 URL 最长100字节";
"TUIKitErrorSDKGroupInvalidNameCard" = "群名片非法，群名片最长50字节";
"TUIKitErrorSDKGroupMemberCountLimit" = "超过群组成员数的限制";
"TUIKitErrorSDKGroupJoinPrivateGroupDeny" = "不允许申请加入 Private 群组";
"TUIKitErrorSDKGroupInviteSuperDeny" = "不允许邀请角色为群主的成员";
"TUIKitErrorSDKGroupInviteNoMember" = "不允许邀请0个成员";
"TUIKitErrorSDKFriendShipInvalidProfileKey" = "资料字段非法";
"TUIKitErrorSDKFriendshipInvalidAddRemark" = "备注字段非法，最大96字节";
"TUIKitErrorSDKFriendshipInvalidAddWording" = "请求添加好友的请求说明字段非法，最大120字节";
"TUIKitErrorSDKFriendshipInvalidAddSource" = "请求添加好友的添加来源字段非法，来源需要添加“AddSource_Type_”前缀。";
"TUIKitErrorSDKFriendshipFriendGroupEmpty" = "好友分组字段非法，必须不为空，每个分组的名称最长30字节";
"TUIKitErrorSDKNetEncodeFailed" = "网络链接加密失败";
"TUIKitErrorSDKNetDecodeFailed" = "网络链接解密失败";
"TUIKitErrorSDKNetAuthInvalid" = "网络链接未完成鉴权";
"TUIKitErrorSDKNetCompressFailed" = "数据包压缩失败";
"TUIKitErrorSDKNetUncompressFaile" = "数据包解压失败";
"TUIKitErrorSDKNetFreqLimit" = "调用频率限制，最大每秒发起 5 次请求";
"TUIKitErrorSDKnetReqCountLimit" = "请求队列満，超过同时请求的数量限制，最大同时发起1000个请求。";
"TUIKitErrorSDKNetDisconnect" = "网络已断开，未建立连接，或者建立 socket 连接时，检测到无网络。";
"TUIKitErrorSDKNetAllreadyConn" = "网络连接已建立，重复创建连接";
"TUIKitErrorSDKNetConnTimeout" = "建立网络连接超时，请等网络恢复后重试。";
"TUIKitErrorSDKNetConnRefuse" = "网络连接已被拒绝，请求过于频繁，服务端拒绝服务。";
"TUIKitErrorSDKNetNetUnreach" = "没有到达网络的可用路由，请等网络恢复后重试。";
"TUIKitErrorSDKNetSocketNoBuff" = "系统中没有足够的缓冲区空间资源可用来完成调用，系统过于繁忙，内部错误。";
"TUIKitERRORSDKNetResetByPeer" = "对端重置了连接";
"TUIKitErrorSDKNetSOcketInvalid" = "socket 套接字无效";
"TUIKitErrorSDKNetHostGetAddressFailed" = "IP 地址解析失败";
"TUIKitErrorSDKNetConnectReset" = "网络连接到中间节点或服务端重置";
"TUIKitErrorSDKNetWaitInQueueTimeout" = "请求包等待进入待发送队列超时";
"TUIKitErrorSDKNetWaitSendTimeout" = "请求包已进入待发送队列，等待进入系统的网络 buffer 超时";
"TUIKitErrorSDKNetWaitAckTimeut" = "请求包已进入系统的网络 buffer ，等待服务端回包超时";
"TUIKitErrorSDKSVRSSOConnectLimit" = "Server 的连接数量超出限制，服务端拒绝服务。";
"TUIKitErrorSDKSVRSSOVCode" = "验证码下发超时。";
"TUIKitErrorSVRSSOD2Expired" = "Key 过期。Key 是根据 UserSig 生成的内部票据，Key 的有效期小于或等于 UserSig 的有效期。请重新调用 TIMManager.getInstance().login 登录接口生成新的 Key。";
"TUIKitErrorSVRA2UpInvalid" = "Ticket 过期。Ticket 是根据 UserSig 生成的内部票据，Ticket 的有效期小于或等于 UserSig 的有效期。请重新调用 TIMManager.getInstance().login 登录接口生成新的 Ticket。";
"TUIKitErrorSVRA2DownInvalid" = "票据验证没通过或者被安全打击。请重新调用 TIMManager.getInstance().login 登录接口生成新的票据。";
"TUIKitErrorSVRSSOEmpeyKey" = "不允许空 Key。";
"TUIKitErrorSVRSSOUinInvalid" = "Key 中的帐号和请求包头的帐号不匹配。";
"TUIKitErrorSVRSSOVCodeTimeout" = "验证码下发超时。";
"TUIKitErrorSVRSSONoImeiAndA2" = "需要带上 Key 和 Ticket。";
"TUIKitErrorSVRSSOCookieInvalid" = "Cookie 检查不匹配。";
"TUIKitErrorSVRSSODownTips" = "下发提示语，Key 过期。";
"TUIKitErrorSVRSSODisconnect" = "断链锁屏。";
"TUIKitErrorSVRSSOIdentifierInvalid" = "失效身份。";
"TUIKitErrorSVRSSOClientClose" = "终端自动退出。";
"TUIKitErrorSVRSSOMSFSDKQuit" = "MSFSDK 自动退出。";
"TUIKitErrorSVRSSOD2KeyWrong" = "解密失败次数超过阈值，通知终端需要重置，请重新调用 TIMManager.getInstance().login 登录接口生成新的 Key。";
"TUIKitErrorSVRSSOUnsupport" = "不支持聚合，给终端返回统一的错误码。终端在该 TCP 长连接上停止聚合。";
"TUIKitErrorSVRSSOPrepaidArrears" = "预付费欠费。";
"TUIKitErrorSVRSSOPacketWrong" = "请求包格式错误。";
"TUIKitErrorSVRSSOAppidBlackList" = "SDKAppID 黑名单。";
"TUIKitErrorSVRSSOCmdBlackList" = "SDKAppID 设置 service cmd 黑名单。";
"TUIKitErrorSVRSSOAppidWithoutUsing" = "SDKAppID 停用。";
"TUIKitErrorSVRSSOFreqLimit" = "频率限制(用户)，频率限制是设置针对某一个协议的每秒请求数的限制。";
"TUIKitErrorSVRSSOOverload" = "过载丢包(系统)，连接的服务端处理过多请求，处理不过来，拒绝服务。";
"TUIKitErrorSVRResNotFound" = "要发送的资源文件不存在。";
"TUIKitErrorSVRResAccessDeny" = "要发送的资源文件不允许访问。";
"TUIKitErrorSVRResSizeLimit"= "文件大小超过限制。";
"TUIKitErrorSVRResSendCancel" = "用户取消发送，如发送过程中登出等原因。";
"TUIKitErrorSVRResReadFailed" = "读取文件内容失败。";
"TUIKitErrorSVRResTransferTimeout" = "资源文件传输超时";
"TUIKitErrorSVRResInvalidParameters" = "参数非法。";
"TUIKitErrorSVRResInvalidFileMd5" = "文件 MD5 校验失败。";
"TUIKitErrorSVRResInvalidPartMd5" = "分片 MD5 校验失败。";
"TUIKitErrorSVRCommonInvalidHttpUrl" = "HTTP 解析错误 ，请检查 HTTP 请求 URL 格式。";
"TUIKitErrorSVRCommomReqJsonParseFailed" = "HTTP 请求 JSON 解析错误，请检查 JSON 格式。";
"TUIKitErrorSVRCommonInvalidAccount" = "请求 URI 或 JSON 包体中 Identifier 或 UserSig 错误。";
"TUIKitErrorSVRCommonInvalidSdkappid" = "SDKAppID 失效，请核对 SDKAppID 有效性。";
"TUIKitErrorSVRCommonRestFreqLimit" = "REST 接口调用频率超过限制，请降低请求频率。";
"TUIKitErrorSVRCommonRequestTimeout" = "服务请求超时或 HTTP 请求格式错误，请检查并重试。";
"TUIKitErrorSVRCommonInvalidRes" = "请求资源错误，请检查请求 URL。";
"TUIKitErrorSVRCommonIDNotAdmin" = "REST API 请求的 Identifier 字段请填写 App 管理员帐号。";
"TUIKitErrorSVRCommonSdkappidFreqLimit" = "SDKAppID 请求频率超限，请降低请求频率。";
"TUIKitErrorSVRCommonSdkappidMiss" = "REST 接口需要带 SDKAppID，请检查请求 URL 中的 SDKAppID。";
"TUIKitErrorSVRCommonRspJsonParseFailed" = "HTTP 响应包 JSON 解析错误。";
"TUIKitErrorSVRCommonExchangeAccountTimeout" = "置换帐号超时。";
"TUIKitErrorSVRCommonInvalidIdFormat" = "请求包体 Identifier 类型错误，请确认 Identifier 为字符串格式。";
"TUIKitErrorSVRCommonSDkappidForbidden" = "SDKAppID 被禁用";
"TUIKitErrorSVRCommonReqForbidden" = "请求被禁用";
"TUIKitErrorSVRCommonReqFreqLimit" = "请求过于频繁，请稍后重试。";
"TUIKitErrorSVRCommonInvalidService" = "您的专业版套餐包已到期并停用，请登录 即时通信 IM 购买页面 重新购买套餐包。购买后，将在5分钟后生效。";
"TUIKitErrorSVRCommonSensitiveText" = "文本安全打击，文本中可能包含敏感词汇。";
"TUIKitErrorSVRCommonBodySizeLimit" = "发消息包体过长，目前支持最大8k消息包体长度，请减少包体大小重试。";
"TUIKitErrorSVRAccountUserSigExpired" = "UserSig 已过期，请重新生成 UserSig";
"TUIKitErrorSVRAccountUserSigEmpty" = "UserSig 长度为0";
"TUIKitErrorSVRAccountUserSigCheckFailed" = "UserSig 校验失败";
"TUIKitErrorSVRAccountUserSigMismatchPublicKey" = "用公钥验证 UserSig 失败";
"TUIKitErrorSVRAccountUserSigMismatchId" = "请求的 Identifier 与生成 UserSig 的 Identifier 不匹配。";
"TUIKitErrorSVRAccountUserSigMismatchSdkAppid" = "请求的 SDKAppID 与生成 UserSig 的 SDKAppID 不匹配。";
"TUIKitErrorSVRAccountUserSigPublicKeyNotFound" = "验证 UserSig 时公钥不存在";
"TUIKitErrorSVRAccountUserSigSdkAppidNotFount" = "SDKAppID 未找到，请在云通信 IM 控制台确认应用信息。";
"TUIKitErrorSVRAccountInvalidUserSig" = "UserSig 已经失效，请重新生成，再次尝试。";
"TUIKitErrorSVRAccountNotFound" = "请求的用户帐号不存在。";
"TUIKitErrorSVRAccountSecRstr" = "安全原因被限制。";
"TUIKitErrorSVRAccountInternalTimeout" = "服务端内部超时，请重试。";
"TUIKitErrorSVRAccountInvalidCount" = "请求中批量数量不合法。";
"TUIkitErrorSVRAccountINvalidParameters" = "参数非法，请检查必填字段是否填充，或者字段的填充是否满足协议要求。";
"TUIKitErrorSVRAccountAdminRequired" = "请求需要 App 管理员权限。";
"TUIKitErrorSVRAccountFreqLimit" = "因失败且重试次数过多导致被限制，请检查 UserSig 是否正确，一分钟之后再试。";
"TUIKitErrorSVRAccountBlackList" = "帐号被拉入黑名单。";
"TUIKitErrorSVRAccountCountLimit" = "创建帐号数量超过免费体验版数量限制，请升级为专业版。";
"TUIKitErrorSVRAccountInternalError" = "服务端内部错误，请重试。";
"TUIKitErrorSVRProfileInvalidParameters" = "请求参数错误，请根据错误描述检查请求是否正确。";
"TUIKitErrorSVRProfileAccountMiss" = "请求参数错误，没有指定需要拉取资料的用户帐号。";
"TUIKitErrorSVRProfileAccountNotFound" = "请求的用户帐号不存在。";
"TUIKitErrorSVRProfileAdminRequired" = "请求需要 App 管理员权限。";
"TUIKitErrorSVRProfileSensitiveText" = "资料字段中包含敏感词。";
"TUIKitErrorSVRProfileInternalError" = "服务端内部错误，请稍后重试。";
"TUIKitErrorSVRProfileReadWritePermissionRequired" = "没有资料字段的读权限，详情可参见 资料字段。";
"TUIKitErrorSVRProfileTagNotFound" = "资料字段的 Tag 不存在。";
"TUIKitErrorSVRProfileSizeLimit" = "资料字段的 Value 长度超过500字节。";
"TUIKitErrorSVRProfileValueError" = "标配资料字段的 Value 错误，详情可参见 标配资料字段。";
"TUIKitErrorSVRProfileInvalidValueFormat" = "资料字段的 Value 类型不匹配，详情可参见 标配资料字段。";
"TUIKitErrorSVRFriendshipInvalidParameters" = "请求参数错误，请根据错误描述检查请求是否正确。";
"TUIKitErrorSVRFriendshipInvalidSdkAppid" = "SDKAppID 不匹配。";
"TUIKitErrorSVRFriendshipAccountNotFound" = "请求的用户帐号不存在。";
"TUIKitErrorSVRFriendshipAdminRequired" = "请求需要 App 管理员权限。";
"TUIKitErrorSVRFriendshipSensitiveText" = "关系链字段中包含敏感词。";
"TUIKitErrorSVRFriendshipNetTimeout" = "网络超时，请稍后重试。";
"TUIKitErrorSVRFriendshipWriteConflict" = "并发写导致写冲突，建议使用批量方式。";
"TUIKitErrorSVRFriendshipAddFriendDeny" = "后台禁止该用户发起加好友请求。";
"TUIkitErrorSVRFriendshipCountLimit" = "自己的好友数已达系统上限。";
"TUIKitErrorSVRFriendshipGroupCountLimit" = "分组已达系统上限。";
"TUIKitErrorSVRFriendshipPendencyLimit" = "未决数已达系统上限。";
"TUIKitErrorSVRFriendshipBlacklistLimit" = "黑名单数已达系统上限。";
"TUIKitErrorSVRFriendshipPeerFriendLimit" = "对方的好友数已达系统上限。";
"TUIKitErrorSVRFriendshipInSelfBlacklist" = "对方在自己的黑名单中，不允许加好友。";
"TUIKitErrorSVRFriendshipAllowTypeDenyAny" = "对方的加好友验证方式是不允许任何人添加自己为好友。";
"TUIKitErrorSVRFriendshipInPeerBlackList" = "自己在对方的黑名单中，不允许加好友。";
"TUIKitErrorSVRFriendshipAllowTypeNeedConfirm" = "请求已发送，等待对方同意";
"TUIKitErrorSVRFriendshipAddFriendSecRstr" = "添加好友请求被安全策略打击，请勿频繁发起添加好友请求。";
"TUIKitErrorSVRFriendshipPendencyNotFound" = "请求的未决不存在。";
"TUIKitErrorSVRFriendshipDelFriendSecRstr" = "删除好友请求被安全策略打击，请勿频繁发起删除好友请求。";
"TUIKirErrorSVRFriendAccountNotFoundEx" = "请求的用户帐号不存在。";
"TUIKitErrorSVRMsgPkgParseFailed" = "解析请求包失败。";
"TUIKitErrorSVRMsgInternalAuthFailed" = "内部鉴权失败。";
"TUIKitErrorSVRMsgInvalidId" = "Identifier 无效";
"TUIKitErrorSVRMsgNetError" = "网络异常，请重试。";
"TUIKitErrorSVRMsgPushDeny" = "触发发送单聊消息之前回调，App 后台返回禁止下发该消息。";
"TUIKitErrorSVRMsgInPeerBlackList" = "发送单聊消息，被对方拉黑，禁止发送。";
"TUIKitErrorSVRMsgBothNotFriend" = "消息发送双方互相不是好友，禁止发送。";
"TUIKitErrorSVRMsgNotPeerFriend" = "发送单聊消息，自己不是对方的好友（单向关系），禁止发送。";
"TUIkitErrorSVRMsgNotSelfFriend" = "发送单聊消息，对方不是自己的好友（单向关系），禁止发送。";
"TUIKitErrorSVRMsgShutupDeny" = "因禁言，禁止发送消息。";
"TUIKitErrorSVRMsgRevokeTimeLimit" = "消息撤回超过了时间限制（默认2分钟）。";
"TUIKitErrorSVRMsgDelRambleInternalError" = "删除漫游内部错误。";
"TUIKitErrorSVRMsgJsonParseFailed" = "JSON 格式解析失败，请检查请求包是否符合 JSON 规范。";
"TUIKitErrorSVRMsgInvalidJsonBodyFormat" = "JSON 格式请求包中 MsgBody 不符合消息格式描述";
"TUIKitErrorSVRMsgInvalidToAccount" = "JSON 格式请求包体中缺少 To_Account 字段或者 To_Account 字段不是 Integer 类型";
"TUIKitErrorSVRMsgInvalidRand" = "JSON 格式请求包体中缺少 MsgRandom 字段或者 MsgRandom 字段不是 Integer 类型";
"TUIKitErrorSVRMsgInvalidTimestamp" = "JSON 格式请求包体中缺少 MsgTimeStamp 字段或者 MsgTimeStamp 字段不是 Integer 类型";
"TUIKitErrorSVRMsgBodyNotArray" = "JSON 格式请求包体中 MsgBody 类型不是 Array 类型";
"TUIKitErrorSVRMsgInvalidJsonFormat" = "JSON 格式请求包不符合消息格式描述";
"TUIKitErrorSVRMsgToAccountCountLimit" = "批量发消息目标帐号超过500";
"TUIKitErrorSVRMsgToAccountNotFound" = "To_Account 没有注册或不存在";
"TUIKitErrorSVRMsgTimeLimit" = "消息离线存储时间错误（最多不能超过7天）。";
"TUIKitErrorSVRMsgInvalidSyncOtherMachine" = "JSON 格式请求包体中 SyncOtherMachine 字段不是 Integer 类型";
"TUIkitErrorSVRMsgInvalidMsgLifeTime" = "JSON 格式请求包体中 MsgLifeTime 字段不是 Integer 类型";
"TUIKitErrorSVRMsgBodySizeLimit" = "JSON 数据包超长，消息包体请不要超过8k。";
"TUIKitErrorSVRmsgLongPollingCountLimit" = "Web 端长轮询被踢（Web 端同时在线实例个数超出限制）。";
"TUIKitErrorSVRGroupApiNameError" = "请求中的接口名称错误";
"TUIKitErrorSVRGroupAccountCountLimit" = "请求包体中携带的帐号数量过多。";
"TUIkitErrorSVRGroupFreqLimit" = "操作频率限制，请尝试降低调用的频率。";
"TUIKitErrorSVRGroupPermissionDeny" = "操作权限不足";
"TUIKitErrorSVRGroupInvalidReq" = "请求非法";
"TUIKitErrorSVRGroupSuperNotAllowQuit" = "该群不允许群主主动退出。";
"TUIKitErrorSVRGroupNotFound" = "群组不存在";
"TUIKitErrorSVRGroupJsonParseFailed" = "解析 JSON 包体失败，请检查包体的格式是否符合 JSON 格式。";
"TUIKitErrorSVRGroupInvalidId" = "发起操作的 Identifier 非法，请检查发起操作的用户 Identifier 是否填写正确。";
"TUIKitErrorSVRGroupAllreadyMember" = "被邀请加入的用户已经是群成员。";
"TUIKitErrorSVRGroupFullMemberCount" = "群已满员，无法将请求中的用户加入群组";
"TUIKitErrorSVRGroupInvalidGroupId" = "群组 ID 非法，请检查群组 ID 是否填写正确。";
"TUIKitErrorSVRGroupRejectFromThirdParty" = "App 后台通过第三方回调拒绝本次操作。";
"TUIKitErrorSVRGroupShutDeny" = "因被禁言而不能发送消息，请检查发送者是否被设置禁言。";
"TUIKitErrorSVRGroupRspSizeLimit" = "应答包长度超过最大包长";
"TUIKitErrorSVRGroupAccountNotFound" = "请求的用户帐号不存在。";
"TUIKitErrorSVRGroupGroupIdInUse" = "群组 ID 已被使用，请选择其他的群组 ID。";
"TUIKitErrorSVRGroupSendMsgFreqLimit" = "发消息的频率超限，请延长两次发消息时间的间隔。";
"TUIKitErrorSVRGroupReqAllreadyBeenProcessed" = "此邀请或者申请请求已经被处理。";
"TUIKitErrorSVRGroupGroupIdUserdForSuper" = "群组 ID 已被使用，并且操作者为群主，可以直接使用。";
"TUIKitErrorSVRGroupSDkAppidDeny" = "该 SDKAppID 请求的命令字已被禁用";
"TUIKitErrorSVRGroupRevokeMsgNotFound" = "请求撤回的消息不存在。";
"TUIKitErrorSVRGroupRevokeMsgTimeLimit" = "消息撤回超过了时间限制（默认2分钟）。";
"TUIKitErrorSVRGroupRevokeMsgDeny" = "请求撤回的消息不支持撤回操作。";
"TUIKitErrorSVRGroupNotAllowRevokeMsg" = "群组类型不支持消息撤回操作。";
"TUIKitErrorSVRGroupRemoveMsgDeny" = "该消息类型不支持删除操作。";
"TUIKitErrorSVRGroupNotAllowRemoveMsg" = "音视频聊天室和在线成员广播大群不支持删除消息。";
"TUIKitErrorSVRGroupAvchatRoomCountLimit" = "音视频聊天室创建数量超过了限制";
"TUIKitErrorSVRGroupCountLimit" = "单个用户可创建和加入的群组数量超过了限制”。";
"TUIKitErrorSVRGroupMemberCountLimit" = "群成员数量超过限制";
"TUIKitErrorSVRNoSuccessResult" = "批量操作无成功结果";
"TUIKitErrorSVRToUserInvalid" = "IM: 无效接收方";
"TUIKitErrorSVRRequestTimeout" = "请求超时";
"TUIKitErrorSVRInitCoreFail" = "INIT CORE模块失败";
"TUIKitErrorExpiredSessionNode" = "SessionNode为null";
"TUIKitErrorLoggedOutBeforeLoginFinished" = "在登录完成前进行了登出（在登录时返回）";
"TUIKitErrorTLSSDKNotInitialized" = "tlssdk未初始化";
"TUIKitErrorTLSSDKUserNotFound" = "TLSSDK没有找到相应的用户信息";
"TUIKitErrorBindFaildRegTimeout" = "注册超时";
"TUIKitErrorBindFaildIsBinding" = "正在bind操作中";
"TUIKitErrorPacketFailUnknown" = "发包未知错误";
"TUIKitErrorPacketFailReqNoNet" = "发送请求包时没有网络,处理时转换成case ERR_REQ_NO_NET_ON_REQ:";
"TUIKitErrorPacketFailRespNoNet" = "发送回复包时没有网络,处理时转换成case ERR_REQ_NO_NET_ON_RSP:";
"TUIKitErrorPacketFailReqNoAuth" = "发送请求包时没有权限";
"TUIKitErrorPacketFailSSOErr" = "SSO错误";
"TUIKitErrorPacketFailRespTimeout" = "回复超时";
"TUIKitErrorFriendshipProxySyncing" = "proxy_manager没有完成svr数据同步";
"TUIKitErrorFriendshipProxySyncedFail" = "proxy_manager同步失败";
"TUIKitErrorFriendshipProxyLocalCheckErr" = "proxy_manager请求参数，在本地检查不合法";
"TUIKitErrorGroupInvalidField" = "group assistant请求字段中包含非预设字段";
"TUIKitErrorGroupStoreageDisabled" = "group assistant群资料本地存储没有开启";
"TUIKitErrorLoadGrpInfoFailed" = "无法从存储中加载groupinfo";
"TUIKitErrorReqNoNetOnReq" = "请求的时候没有网络";
"TUIKitErrorReqNoNetOnResp" = "响应的时候没有网络";
"TUIKitErrorServiceNotReady" = "QALSDK服务未就绪";
"TUIKitErrorLoginAuthFailed" = "账号认证失败（用户信息获取失败）";
"TUIKitErrorNeverConnectAfterLaunch" = "在应用启动后没有尝试联网";
"TUIKitErrorReqFailed" = "QAL执行失败";
"TUIKitErrorReqInvaidReq" = "请求非法，toMsgService非法";
"TUIKitErrorReqOnverLoaded" = "请求队列满";
"TUIKitErrorReqKickOff" = "已经被其他终端踢了";
"TUIKitErrorReqServiceSuspend" = "服务被暂停";
"TUIKitErrorReqInvalidSign" = "SSO签名错误";
"TUIKitErrorReqInvalidCookie" = "SSO cookie无效";
"TUIKitErrorLoginTlsRspParseFailed" = "登录时TLS回包校验，包体长度错误";
"TUIKitErrorLoginOpenMsgTimeout" = "登录时OPENSTATSVC向OPENMSG上报状态超时";
"TUIKitErrorLoginOpenMsgRspParseFailed" = "登录时OPENSTATSVC向OPENMSG上报状态时解析回包失败";
"TUIKitErrorLoginTslDecryptFailed" = "登录时TLS解密失败";
"TUIKitErrorWifiNeedAuth" = "wifi需要认证";
"TUIKitErrorUserCanceled" = "用户已取消";
"TUIkitErrorRevokeTimeLimitExceed" = "消息撤回超过了时间限制（默认2分钟）";
"TUIKitErrorLackUGExt" = "缺少UGC扩展包";
"TUIKitErrorAutoLoginNeedUserSig" = "自动登录，本地票据过期，需要userSig手动登录";
"TUIKitErrorQALNoShortConneAvailable" = "没有可用的短连接sso";
"TUIKitErrorReqContentAttach" = "消息内容安全打击";
"TUIKitErrorLoginSigExpire" = "登录返回，票据过期";
"TUIKitErrorSDKHadInit" = "SDK 已经初始化无需重复初始化";
"TUIKitErrorOpenBDHBase" = "openbdh 错误码";
"TUIKitErrorRequestNoNetOnReq" = "请求时没有网络，请等网络恢复后重试";
"TUIKitErrorRequestNoNetOnRsp" = "响应时没有网络，请等网络恢复后重试";
"TUIKitErrorRequestOnverLoaded" = "请求队列満";

/***************************** 消息转发 & 消息搜索 *************************/
// 补充
"Multiple" = "多选";
"Forward" = "转发";
"Delete" = "删除";
"Copy" = "复制";
"Revoke" = "撤回";
"Resend" = "重发";
"Search" = "搜索";
"TUIKitCallMicCamAuthTips" = "请开启麦克风和摄像头权限";
"TUIKitCallMicAuthTips" = "请开启麦克风权限";
"TUIKitMessageTipsSureToResend" = "确定重发消息?";

// 消息转发
"TUIKitRelayNoMessageTips" = "请选择消息";
"TUIKitRelayRecentMessages" = "最近聊天";
"TUIKitRelayChatHistory" = "聊天记录";
"TUIKitRelaySepcialForbid" = "选中的消息中，语音消息等其他消息不能被转发.";
"TUIKitRelayConfirmForward" = "确定转发？";
"TUIKitRelayOneByOneForward" = "逐条转发";
"TUIKitRelayCombineForwad" = "合并转发";
"TUIKitRelayGroupChatHistory" = "群聊的聊天记录";
"TUIKitRelayChatHistoryForSomebodyFormat" = "%@和%@的聊天记录";
"TUIKitRelayErrorForwardFormat" = "@\"转发失败, code:%d, desc:%@\"";
"TUIKitRelayTargetCreateNewChat" = "创建新聊天";
"TUIKitRelayTargetSelectFromContacts" = "从联系人中选择";
"TUIKitRelayTargetCrateGroupError" = "创建群失败";
"TUIKitRelayTargetNoneTips" = "请选择联系人或会话";
"TUIKitRelayLayerLimitTips" = "抱歉，当前嵌套层级超过了限制";
"TUIKitRelayCompatibleText" = "不支持合并转发消息，请升级到最新版本。";
"TUIKitRelayUnsupportForward" = "发送失败的消息不支持转发";
"TUIKitRelayOneByOnyOverLimit" = "转发消息过多，暂不支持逐条转发";

// 消息搜索
"TUIKitSearchItemHeaderTitleContact" = "联系人";
"TUIKitSearchItemFooterTitleContact" = "查看更多联系人";
"TUIKitSearchItemHeaderTitleGroup" = "群聊";
"TUIKitSearchItemFooterTitleGroup" = "查看更多群聊";
"TUIkitSearchItemHeaderTitleChatHistory" = "聊天记录";
"TUIKitSearchItemFooterTitleChatHistory" = "查看更多聊天记录";
"TUIKitSearchResultMatchFormat" = "包含:%@";
"TUIKitSearchResultMatchGroupIDFormat" = "包含群ID:%@";
"TUIKitSearchResultMatchGroupMember" = "包含群成员:";
"TUIKitSearchResultDisplayChatHistoryCountFormat" = "%zd条聊天记录";
/***************************** 消息转发 & 消息搜索 *************************/


/***************************** 消息回复 & 换肤 *************************/
"Reply" = "回复";
"TUIKitReplyMessageNotFoundOriginMessage" = "无法定位到原消息";
"TUIKitClearAllChatHistory" = "清除聊天记录";
"TUIKitClearAllChatHistoryTips" = "确定清空聊天记录？";
"Discline" = "拒绝";
"Copied" = "已复制";
"ConfirmDeleteMessage" = "确定删除已选消息";
"TUIKitAddFriend" = "添加好友";
"TUIKitAddGroup" = "添加群组";
"TUIKitSearchUserID" = "搜索用户ID";
"TUIKitSearchGroupID" = "搜索群ID";
"TUIKitNoSelfSignature" = "暂无个性签名";
"TUIKitSelfSignatureFormat" = "个性签名:%@";
/***************************** 消息回复 & 换肤 *************************/


/***************************** 视频、图片加载 & 保存 *************************/
"TUIKitVideoTranscoding" = "视频转码中...";
"TUIKitVideoDownloading" = "视频下载中...";
"TUIKitVideoSavedSuccess" = "视频保存成功";
"TUIKitVideoSavedFailed" = "视频保存失败";
"TUIKitPictureSavedSuccess" = "图片保存成功";
"TUIKitPictureSavedFailed" = "图片保存失败";
/***************************** 视频、图片加载 & 保存 *************************/
